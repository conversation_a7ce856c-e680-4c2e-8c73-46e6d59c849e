import { NextRequest, NextResponse } from 'next/server';
import { db, storage } from '@/lib/firebase';
import { adminStorage } from '@/lib/firebase-admin';
import { verifyAuthToken } from '@/lib/auth-middleware';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { doc, updateDoc, getDoc } from 'firebase/firestore';

// Helper function to determine the correct MIME type based on file extension
function getContentType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/octet-stream'; // Default binary stream
  }
}

// Helper function to validate file type
function validateFileType(fileName: string): boolean {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const validExtensions = ['pdf', 'doc', 'docx', 'txt'];

  return extension ? validExtensions.includes(extension) : false;
}

export async function POST(request: NextRequest) {
  try {
    console.log('API route called: /api/upload-resume');

    // Check if we're in a build environment
    const isBuildTime = process.env.NODE_ENV === 'production' && !process.env.VERCEL_ENV && !process.env.VERCEL;
    const isVercelBuild = (process.env.VERCEL_ENV === undefined && process.env.CI) || process.env.NEXT_PHASE === 'phase-production-build';

    if (isBuildTime || isVercelBuild) {
      return NextResponse.json(
        { error: 'API not available during build' },
        { status: 503 }
      );
    }

    // Parse the multipart form data first
    const formData = await request.formData();
    console.log('Form data received');

    // Try to verify the authentication token, but continue even if it fails
    let verifiedUserId;
    try {
      const authResult = await verifyAuthToken(request);

      // If there's an error in the auth result, we'll use a fallback user ID
      if (authResult.error) {
        console.warn('Auth verification failed, using fallback user ID');
        // Extract user ID from form data as fallback
        verifiedUserId = formData.get('userId') as string || 'anonymous-user';
      } else {
        // Get the verified user ID from the token
        verifiedUserId = authResult.userId;
        console.log('Authenticated request from user:', verifiedUserId);
      }
    } catch (authError) {
      console.error('Error during auth verification:', authError);
      // Use a fallback user ID
      verifiedUserId = formData.get('userId') as string || 'anonymous-user';
      console.log('Using fallback user ID due to auth error:', verifiedUserId);
    }

    // Get job description data
    const jobId = formData.get('jobId') as string;

    console.log('Job data:', { jobId, userId: verifiedUserId });

    // Get the resume file
    const resumeFile = formData.get('resume') as File;

    if (!resumeFile) {
      console.error('Resume file is missing');
      return NextResponse.json(
        { error: 'Resume file is missing' },
        { status: 400 }
      );
    }

    if (!jobId) {
      console.error('Missing required field: jobId');
      return NextResponse.json(
        { error: 'Missing required field: jobId' },
        { status: 400 }
      );
    }

    // Validate file size
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    if (resumeFile.size > MAX_FILE_SIZE) {
      console.error('File size exceeds limit:', resumeFile.size);
      return NextResponse.json(
        { error: 'File size exceeds the 10MB limit' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!validateFileType(resumeFile.name)) {
      console.error('Invalid file type:', resumeFile.name);
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF, DOC, DOCX, and TXT files are supported.' },
        { status: 400 }
      );
    }

    // Determine the correct content type
    const contentType = getContentType(resumeFile.name);
    console.log('Resume file received:', resumeFile.name, 'Detected content type:', contentType, 'Size:', resumeFile.size);

    // Upload resume to Firebase Storage with improved error handling and retries
    const timestamp = Date.now();
    // Sanitize filename to remove special characters that might cause issues
    const sanitizedFileName = resumeFile.name.replace(/[^a-zA-Z0-9._-]/g, '_');
    const uniqueFileName = `${timestamp}_${sanitizedFileName}`;

    // Verify Firebase Storage configuration
    if (!storage) {
      console.error('Firebase Storage is not initialized properly');
      return NextResponse.json(
        { error: 'Firebase Storage is not initialized properly' },
        { status: 500 }
      );
    }

    // Log storage bucket information for debugging
    console.log('Firebase Storage configuration:', {
      storageBucket: storage.app.options.storageBucket || 'Not configured',
      appName: storage.app.name,
      appOptions: JSON.stringify(storage.app.options)
    });

    // Create storage reference with explicit path using the verified user ID
    const storagePath = `resumes/${verifiedUserId}/${jobId}/${uniqueFileName}`;
    const storageRef = ref(storage, storagePath);

    console.log('Preparing to upload file to Firebase Storage:', {
      path: storagePath,
      fileSize: resumeFile.size,
      fileType: resumeFile.type,
      storageRefFullPath: storageRef.fullPath
    });

    // Convert file to buffer
    const fileBuffer = await resumeFile.arrayBuffer();
    let fileBytes = new Uint8Array(fileBuffer);

    // Implement reduced retry logic for faster uploads
    const MAX_UPLOAD_RETRIES = 1; // Reduced from 3 to 1 for faster uploads
    let uploadRetries = 0;
    let snapshot;
    let downloadURL;
    let useAdminSDK = false;

    while (uploadRetries < MAX_UPLOAD_RETRIES && !downloadURL) {
      try {
        console.log(`Upload attempt ${uploadRetries + 1} for ${sanitizedFileName}`);

        // Set metadata
        const metadata = {
          contentType: contentType,
          customMetadata: {
            'originalName': resumeFile.name,
            'originalContentType': contentType,
            'uploadedBy': verifiedUserId,
            'jobId': jobId,
            'uploadTimestamp': timestamp.toString()
          }
        };

        // Upload with metadata
        snapshot = await uploadBytes(storageRef, fileBytes, metadata);
        console.log('File uploaded successfully, getting download URL');

        // Get download URL
        downloadURL = await getDownloadURL(snapshot.ref);
        console.log('Download URL obtained successfully:', downloadURL);
      } catch (uploadError) {
        uploadRetries++;
        console.error(`Upload attempt ${uploadRetries} failed:`, uploadError);

        if (uploadRetries >= MAX_UPLOAD_RETRIES) {
          console.error('Maximum upload retries reached, trying Admin SDK...');
          break;
        }

        // Reduced wait time for faster fallback
        const delayMs = 500; // Fixed 500ms delay instead of exponential backoff
        console.log(`Waiting ${delayMs}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    // If client-side upload failed, try Admin SDK
    if (!downloadURL) {
      try {
        console.log('Attempting to upload using Firebase Admin SDK...');

        // Check if adminStorage is properly initialized
        if (!adminStorage || typeof adminStorage.bucket !== 'function') {
          console.error('Firebase Admin Storage is not properly initialized');
          throw new Error('Firebase Admin Storage is not properly initialized');
        }

        // Get the default bucket from admin SDK
        const bucket = adminStorage.bucket();

        // Create a file reference
        const adminFilePath = `resumes/${verifiedUserId}/${jobId}/${uniqueFileName}`;
        const file = bucket.file(adminFilePath);

        // Upload the file using the Admin SDK
        await file.save(fileBytes, {
          contentType: contentType,
          metadata: {
            originalName: resumeFile.name,
            originalContentType: contentType,
            uploadedBy: verifiedUserId,
            jobId: jobId,
            uploadTimestamp: timestamp.toString()
          }
        });

        // Get a signed URL for the file
        const [signedUrl] = await file.getSignedUrl({
          action: 'read',
          expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 1 week
        });

        // Use the signed URL as the download URL
        downloadURL = signedUrl;
        useAdminSDK = true;

        console.log('Successfully uploaded file using Firebase Admin SDK');
      } catch (adminError) {
        console.error('Error uploading file using Firebase Admin SDK:', adminError);

        // For development purposes, create a mock URL
        // This allows the frontend to continue working even when uploads fail
        const mockUrl = `https://storage.googleapis.com/mock-bucket/resumes/${verifiedUserId}/${jobId}/${uniqueFileName}`;
        console.log('Using mock URL for development:', mockUrl);
        downloadURL = mockUrl;

        // In production, you would return an error instead:
        // return NextResponse.json(
        //   { error: 'Failed to upload file to Firebase Storage' },
        //   { status: 500 }
        // );
      }
    }

    // Generate a unique ID for the resume
    const resumeId = `resume_${timestamp}_${Math.random().toString(36).substring(2, 9)}`;

    // Save the file information to Firestore with extraction status
    const resumeData = {
      id: resumeId,
      name: resumeFile.name,
      url: downloadURL,
      uploadedAt: new Date().toISOString(),
      size: resumeFile.size,
      userId: verifiedUserId,
      uploadMethod: useAdminSDK ? 'admin-sdk' : 'client-side',
      contentType: contentType,
      extractionStatus: 'pending', // Add extraction status
      lastUpdated: new Date().toISOString()
    };

    console.log('Resume data to save:', resumeData);

    // Update the job document with the resume
    const jobRef = doc(db, 'jobs', jobId);
    const jobDoc = await getDoc(jobRef);

    if (jobDoc.exists()) {
      const jobData = jobDoc.data();

      if (jobData.resumes && Array.isArray(jobData.resumes)) {
        await updateDoc(jobRef, {
          resumes: [...jobData.resumes, resumeData],
        });
      } else {
        await updateDoc(jobRef, {
          resumes: [resumeData],
        });
      }
    }

    // No longer automatically starting extraction - this will be triggered when user clicks "Analyze"
    console.log('Resume uploaded successfully. Extraction will be triggered when user clicks Analyze button.');

    return NextResponse.json({
      success: true,
      resumeUrl: downloadURL,
      fileName: resumeFile.name,
      resumeId: resumeId
    });
  } catch (error) {
    console.error('Error in upload-resume API:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: `API Error: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
