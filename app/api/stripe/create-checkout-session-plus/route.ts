import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_CONFIG } from '@/lib/stripe';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Checking user data for Plus plan checkout:', userId);

    // Get user data from Firestore
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    const userEmail = userData.email;

    // Check if user already has a Plus subscription
    if (userData.tier === 'Plus' && userData.stripeSubscriptionId) {
      return NextResponse.json(
        { error: 'User already has an active Plus subscription' },
        { status: 400 }
      );
    }

    // Create or retrieve Stripe customer
    let customerId = userData.stripeCustomerId;
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: userEmail,
        name: userData.fullName,
        metadata: {
          userId: userId,
          tier: 'Free',
        },
      });
      customerId = customer.id;
    }

    // Create checkout session
    console.log('💳 Creating Stripe checkout session for Plus plan, customer:', customerId);
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: STRIPE_CONFIG.CURRENCY,
            product_data: {
              name: 'Plus Plan',
              description: 'Perfect for growing HR teams - 100 jobs per month, 100 resumes per job',
            },
            unit_amount: STRIPE_CONFIG.PLUS_PLAN_AMOUNT,
            recurring: {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?canceled=true`,
      metadata: {
        userId: userId,
        plan: 'plus',
      },
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
    });

    console.log('✅ Checkout session created successfully:', session.id);

    // Update user with customer ID if it was just created
    if (!userData.stripeCustomerId) {
      await updateDoc(userRef, {
        stripeCustomerId: customerId,
        updatedAt: new Date(),
      });
    }

    return NextResponse.json({ 
      url: session.url,
      sessionId: session.id 
    });

  } catch (error) {
    console.error('❌ Error creating Plus plan checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
