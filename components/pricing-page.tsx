"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check, Sparkles, ChevronDown, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { useAuthState } from "react-firebase-hooks/auth"
import { auth } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

export default function PricingPage() {
  const router = useRouter()
  const [user, loading] = useAuthState(auth)
  const { toast } = useToast()
  const [isProcessingPayment, setIsProcessingPayment] = useState(false)

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleGetStarted = async (plan: string) => {
    if (plan === 'free') {
      router.push("/signup")
    } else if (plan === 'pro') {
      await handleProPlanPurchase()
    } else if (plan === 'plus') {
      await handlePlusPlanPurchase()
    }
  }

  const handleProPlanPurchase = async () => {
    console.log('🚀 Pro plan purchase initiated');
    console.log('👤 Current user:', user);

    if (!user) {
      console.log('❌ No user logged in, redirecting to signup');
      // Redirect to signup if not logged in
      router.push("/signup")
      return
    }

    console.log('💳 Starting payment process for user:', user.uid);
    setIsProcessingPayment(true)

    try {
      console.log('📡 Calling Stripe API...');
      // Create checkout session
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
        }),
      })

      console.log('📥 API Response status:', response.status);
      const data = await response.json()
      console.log('📄 API Response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        console.log('🔗 Redirecting to Stripe checkout:', data.url);
        window.location.href = data.url
      } else {
        throw new Error('No checkout URL received')
      }

    } catch (error) {
      console.error('❌ Error creating checkout session:', error)
      toast({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to start checkout process",
        variant: "destructive",
      })
    } finally {
      setIsProcessingPayment(false)
    }
  }

  const handlePlusPlanPurchase = async () => {
    console.log('🚀 Plus plan purchase initiated');
    console.log('👤 Current user:', user);

    if (!user) {
      console.log('❌ No user logged in, redirecting to signup');
      // Redirect to signup if not logged in
      router.push("/signup")
      return
    }

    console.log('💳 Starting payment process for user:', user.uid);
    setIsProcessingPayment(true)

    try {
      console.log('📡 Calling Stripe API for Plus plan...');
      // Create checkout session for Plus plan
      const response = await fetch('/api/stripe/create-checkout-session-plus', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
        }),
      })

      console.log('📥 API Response status:', response.status);
      const data = await response.json()
      console.log('📄 API Response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        console.log('🔗 Redirecting to Stripe checkout:', data.url);
        window.location.href = data.url
      } else {
        throw new Error('No checkout URL received')
      }

    } catch (error) {
      console.error('❌ Error creating checkout session:', error)
      toast({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to start checkout process",
        variant: "destructive",
      })
    } finally {
      setIsProcessingPayment(false)
    }
  }



  const pricingPlans = [
    {
      name: "Free",
      price: 0,
      badge: "FREE",
      badgeColor: "bg-gray-600",
      description: "Perfect for trying out our platform",
      jobsPerMonth: 3,
      resumesPerJob: 50,
      totalResumes: 150,
      dataRetention: "60 days",
      features: [
        "3 jobs per month",
        "50 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Delete Resume after 60 days",
        "1 user only",
      ],
      buttonText: "Start Free",
      buttonStyle: "border border-gray-600 text-gray-300 hover:text-white hover:border-[#1aa8e0] bg-transparent",
    },
    {
      name: "Pro",
      price: 699,
      badge: "POPULAR",
      badgeColor: "bg-gradient-to-r from-[#8529db] to-[#1aa8e0]",
      description: "Ideal for individual HR professionals",
      jobsPerMonth: 50,
      resumesPerJob: 100,
      totalResumes: 5000,
      dataRetention: "180 days",
      features: [
        "50 jobs per month",
        "100 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Delete Resume after 180 days",
        "Priority support",
        "1 user only",
      ],
      buttonText: "Get Started",
      buttonStyle: "bg-gradient-to-r from-[#8529db] to-[#1aa8e0] text-white border-0",
    },
    {
      name: "Plus",
      price: 1499,
      badge: "PREMIUM",
      badgeColor: "bg-gradient-to-r from-[#f59e0b] to-[#d97706]",
      description: "Perfect for growing HR teams",
      jobsPerMonth: 100,
      resumesPerJob: 100,
      totalResumes: 10000,
      dataRetention: "180 days",
      features: [
        "100 jobs per month",
        "100 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Delete Resume after 180 days",
        "Priority support",
        "1 user only",
      ],
      buttonText: "Get Started",
      buttonStyle: "bg-gradient-to-r from-[#f59e0b] to-[#d97706] text-white border-0",
    },
  ]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#8529db] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#1aa8e0] rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-[#64738b] rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="relative z-20 flex items-center justify-between px-8 py-6 lg:px-16">
        <div className="flex items-center gap-2 cursor-pointer" onClick={scrollToTop}>
          <div className="w-8 h-8 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded flex items-center justify-center shadow-lg shadow-[#8529db]/50">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
        </div>

        <nav className="flex items-center gap-6">
          <a href="/" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Home
          </a>
          <a href="/blog" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Blog
          </a>
          <a href="/signup" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Log in / Sign up
          </a>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
            Simple,{" "}
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Transparent
            </span>{" "}
            Pricing
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Choose the perfect plan for your hiring needs. Start free and scale as you grow.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 mb-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingPlans.map((plan, index) => (
            <div
              key={plan.name}
              className={`bg-black/40 backdrop-blur-xl border rounded-2xl p-8 relative transition-all duration-300 hover:scale-105 ${
                plan.name === "Pro"
                  ? "border-[#8529db]/60 shadow-lg shadow-[#8529db]/20"
                  : plan.name === "Plus"
                  ? "border-[#f59e0b]/60 shadow-lg shadow-[#f59e0b]/20"
                  : "border-gray-700/30 hover:border-[#1aa8e0]/60"
              }`}
            >
              {/* Badge */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className={`${plan.badgeColor} text-white border-0 px-4 py-1`}>{plan.badge}</Badge>
              </div>

              {/* Plan Header */}
              <div className="text-center mb-8 mt-4">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 text-sm mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">₹{plan.price}</span>
                  {plan.price > 0 && (
                    <span className="text-gray-400 text-lg">/month</span>
                  )}
                </div>
              </div>

              {/* Usage Stats */}
              <div className="bg-black/30 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-[#1aa8e0]">{plan.jobsPerMonth}</div>
                    <div className="text-xs text-gray-400">Jobs/Month</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[#8529db]">{plan.resumesPerJob}</div>
                    <div className="text-xs text-gray-400">Resumes/Job</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">{plan.totalResumes.toLocaleString()}</div>
                    <div className="text-xs text-gray-400">Total Resumes</div>
                  </div>
                </div>
              </div>



              {/* Features */}
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-[#1aa8e0] flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <Button
                className={`w-full py-3 ${plan.buttonStyle}`}
                onClick={() => handleGetStarted(plan.name.toLowerCase())}
                disabled={(plan.name.toLowerCase() === 'pro' || plan.name.toLowerCase() === 'plus') && isProcessingPayment}
              >
                {(plan.name.toLowerCase() === 'pro' || plan.name.toLowerCase() === 'plus') && isProcessingPayment ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  plan.buttonText
                )}
              </Button>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 max-w-4xl mx-auto px-8 mb-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Frequently Asked{" "}
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
        </div>

        <Accordion type="single" collapsible className="space-y-4">
          <AccordionItem value="item-1" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              Can I change plans anytime?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              No, plan changes are not available. Please choose your plan carefully based on your current needs.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-2" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              What happens if I exceed my limits?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              We'll notify you when you're approaching your limits. Free users can upgrade to Pro or Plus for higher limits.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-3" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              How many users can I add to my plan?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              All plans (Free, Pro, and Plus) include 1 user only. Each plan is designed for individual HR professionals.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-4" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              How long do you keep my resume data?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              Free plan: Resume data is automatically deleted after 60 days. Pro and Plus plans: Resume data is
              kept for 180 days before automatic deletion.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-5" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              Is there a free trial?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              Yes! Our Free plan allows you to test our platform with 3 jobs per month and up to 50 resumes per job at
              no cost.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-black/60 backdrop-blur-xl border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={scrollToTop}>
                <div className="w-8 h-8 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed max-w-md">
                Cut manual screening by 99% and find the perfect candidates instantly.
              </p>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Home
                  </a>
                </li>
                <li>
                  <a href="/pricing" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Blog
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-800 mt-8 pt-6">
            <p className="text-gray-500 text-sm text-center">© 2025 The Consult Now. All rights reserved.</p>
          </div>
        </div>
      </footer>



      <style jsx>{`
        .clean-box {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  )
}
