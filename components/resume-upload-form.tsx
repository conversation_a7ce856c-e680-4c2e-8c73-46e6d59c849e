"use client"

import type React from "react"
import { useState, useCallback, useEffect } from "react"
import { Upload, FileText, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { auth, db } from "@/lib/firebase"
import { trackResumeUpload, trackAnalysisStart, trackAnalysisComplete, trackError } from "@/lib/gtag"
import { doc, getDoc } from "firebase/firestore"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { CircularProgress } from "@/components/ui/circular-progress"



export default function ResumeUploadForm({ jobId }: { jobId: string }) {
  const [files, setFiles] = useState<File[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [jobData, setJobData] = useState<any>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [uploadComplete, setUploadComplete] = useState(false)
  const [uploadMessage, setUploadMessage] = useState("Preparing your files...")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [uploadedResumeIds, setUploadedResumeIds] = useState<string[]>([])
  const [totalFileSize, setTotalFileSize] = useState(0) // Track total size of selected files
  const router = useRouter()
  const { toast } = useToast()

  // Handle client-side only code
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Check if user is authenticated
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setIsLoading(false)
      if (user) {
        setIsAuthenticated(true)
      } else {
        // Redirect to login if not authenticated
        router.push("/login")
        toast({
          title: "Authentication required",
          description: "Please log in to access this page",
          variant: "destructive",
        })
      }
    })

    return () => unsubscribe()
  }, [router, toast])

  // Check if job exists and fetch job data
  useEffect(() => {
    const checkJob = async () => {
      if (!jobId) return

      try {
        const jobRef = doc(db, "jobs", jobId)
        const jobSnap = await getDoc(jobRef)

        if (!jobSnap.exists()) {
          toast({
            title: "Job not found",
            description: "The job you're trying to upload resumes for doesn't exist",
            variant: "destructive",
          })
          router.push("/start-analysing")
          return
        }

        // Store job data
        const data = jobSnap.data()
        setJobData(data)

        // We no longer need to check for analyzed resumes
        // since we're not displaying match results on this page
      } catch (error) {
        console.error("Error checking job:", error)
      }
    }

    if (isAuthenticated && jobId) {
      checkJob()
    }
  }, [isAuthenticated, jobId, router, toast])



  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    const MAX_TOTAL_SIZE = 5 * 1024 * 1024; // 5MB total limit
    const MAX_FILE_COUNT = 50; // 50 files limit
    const droppedFiles = Array.from(e.dataTransfer.files);

    // Filter out files that are too large or exceed limits
    const validFiles = droppedFiles.filter(file => {
      if (file.size > MAX_FILE_SIZE) {
        toast({
          title: "File too large",
          description: `${file.name} exceeds the 5MB limit and will be skipped.`,
          variant: "destructive",
        });
        return false;
      }
      return true;
    });

    // Update files and calculate new total size
    setFiles((prev) => {
      let newFiles = [...prev, ...validFiles];

      // Check file count limit
      if (newFiles.length > MAX_FILE_COUNT) {
        toast({
          title: "Resume limit reached",
          description: "Limit for resumes is 50 only",
          variant: "destructive",
        });
        newFiles = newFiles.slice(0, MAX_FILE_COUNT);
      }

      // Check total size limit
      const newTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
      if (newTotalSize > MAX_TOTAL_SIZE) {
        toast({
          title: "File size limit reached",
          description: "Limit for resumes is 5 MB only",
          variant: "destructive",
        });
        // Keep adding files until we hit the size limit
        let runningTotal = 0;
        newFiles = newFiles.filter(file => {
          if (runningTotal + file.size <= MAX_TOTAL_SIZE) {
            runningTotal += file.size;
            return true;
          }
          return false;
        });
      }

      // Calculate and update total file size
      const finalTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
      setTotalFileSize(finalTotalSize);
      return newFiles;
    });
  }, [toast])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      const MAX_TOTAL_SIZE = 5 * 1024 * 1024; // 5MB total limit
      const MAX_FILE_COUNT = 50; // 50 files limit
      const selectedFiles = Array.from(e.target.files);

      // Filter out files that are too large
      const validFiles = selectedFiles.filter(file => {
        if (file.size > MAX_FILE_SIZE) {
          toast({
            title: "File too large",
            description: `${file.name} exceeds the 5MB limit and will be skipped.`,
            variant: "destructive",
          });
          return false;
        }
        return true;
      });

      // Update files and calculate new total size
      setFiles((prev) => {
        let newFiles = [...prev, ...validFiles];

        // Check file count limit
        if (newFiles.length > MAX_FILE_COUNT) {
          toast({
            title: "Resume limit reached",
            description: "Limit for resumes is 50 only",
            variant: "destructive",
          });
          newFiles = newFiles.slice(0, MAX_FILE_COUNT);
        }

        // Check total size limit
        const newTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
        if (newTotalSize > MAX_TOTAL_SIZE) {
          toast({
            title: "File size limit reached",
            description: "Limit for resumes is 5 MB only",
            variant: "destructive",
          });
          // Keep adding files until we hit the size limit
          let runningTotal = 0;
          newFiles = newFiles.filter(file => {
            if (runningTotal + file.size <= MAX_TOTAL_SIZE) {
              runningTotal += file.size;
              return true;
            }
            return false;
          });
        }

        // Calculate and update total file size
        const finalTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
        setTotalFileSize(finalTotalSize);
        return newFiles;
      });
    }
  }, [toast])

  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prev) => {
      // Remove the file at the specified index
      const newFiles = prev.filter((_, i) => i !== index);
      // Recalculate total file size
      const newTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
      setTotalFileSize(newTotalSize);
      return newFiles;
    });
  }, [])

  const handleReplaceFile = useCallback(
    (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const newFiles = [...files]
        newFiles[index] = e.target.files[0]
        setFiles(newFiles)

        // Recalculate total file size
        const newTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
        setTotalFileSize(newTotalSize);
      }
    },
    [files],
  )

  // Helper function to format file size in a human-readable format
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    // Round to 2 decimal places
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
  }

  const handleUpload = useCallback(async () => {
    if (!jobId || files.length === 0 || !jobData) {
      console.error("Missing required data:", { jobId, filesLength: files.length, jobData: !!jobData });
      toast({
        title: "Missing information",
        description: "Job information or resume files are missing. Please try again.",
        variant: "destructive",
      });
      return;
    }

    // Reset states for new upload but don't show dialog
    setUploadProgress(0)
    setUploadComplete(false)
    setUploadMessage("Preparing your files...")
    setShowUploadDialog(false) // Don't show the dialog
    setIsUploading(true)
    setUploadedResumeIds([])
    setIsAnalyzing(false)

    const currentUser = auth.currentUser

    if (!currentUser) {
      console.error("User is not authenticated");
      toast({
        title: "Authentication required",
        description: "Please log in to process resumes.",
        variant: "destructive",
      });
      setIsUploading(false);
      setShowUploadDialog(false);
      return;
    }

    // Array of motivational messages to show during upload
    const messages = [
      "Just a few seconds more...",
      "Almost there...",
      "Processing your resume...",
      "Getting things ready...",
      "Securely storing your files..."
    ];

    // Start progress animation
    let currentProgress = 0;
    const progressInterval = setInterval(() => {
      // Increment progress
      currentProgress += 1;
      setUploadProgress(currentProgress);

      // Show random messages at certain progress points
      if (currentProgress % 20 === 0 && currentProgress < 90) {
        const randomIndex = Math.floor(Math.random() * messages.length);
        setUploadMessage(messages[randomIndex]);
      }

      // Slow down progress as it approaches 90%
      if (currentProgress >= 90) {
        clearInterval(progressInterval);
      }
    }, 100);

    try {
      // Process files one by one
      const newResults: Array<{
        name: string;
        url: string;
      }> = [];

      // Log all the files we're about to process
      console.log(`Starting to upload ${files.length} files:`, files.map(f => ({ name: f.name, size: f.size, type: f.type })));

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          // Validate file size and type before sending
          if (file.size > 10 * 1024 * 1024) { // 10MB limit
            throw new Error(`File ${file.name} exceeds the 10MB size limit`);
          }

          // Check file extension
          const fileExt = file.name.split('.').pop()?.toLowerCase();
          if (!fileExt || !['pdf', 'doc', 'docx', 'txt'].includes(fileExt)) {
            throw new Error(`File ${file.name} has an unsupported format. Please use PDF, DOC, DOCX, or TXT files.`);
          }

          // Create form data for the API request
          const formData = new FormData();
          formData.append('resume', file);
          formData.append('jobId', jobId);
          formData.append('userId', currentUser.uid);

          console.log(`Uploading file ${i+1}/${files.length}: ${file.name} (${file.size} bytes, ${file.type})`);
          console.log('Form data prepared with jobId:', jobId);
          console.log('Form data contains userId:', currentUser.uid);

          // Update progress message to show which file is being uploaded
          setUploadMessage(`Uploading ${file.name}...`);

          // Implement retry logic for API calls
          const MAX_API_RETRIES = 3;
          let apiRetries = 0;
          let apiSuccess = false;
          let lastError: Error | null = null;

          while (apiRetries < MAX_API_RETRIES && !apiSuccess) {
            try {
              console.log(`API request attempt ${apiRetries + 1} for ${file.name}...`);

              // Get the current user's ID token
              const idToken = await currentUser.getIdToken();

              // Call our API endpoint with improved error handling and authentication token
              const response = await fetch('/api/upload-resume', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${idToken}`
                },
                body: formData,
              });

              console.log('API response status for', file.name, ':', response.status);

              // Clone the response to avoid "body already read" errors
              const responseClone = response.clone();

              // Try to parse the response as JSON with improved error handling
              let responseData;
              try {
                responseData = await response.json();
                console.log('API response data structure:', Object.keys(responseData));
              } catch (jsonError) {
                console.error('Failed to parse JSON response:', jsonError);

                // Use the cloned response to get text
                try {
                  const textResponse = await responseClone.text();
                  console.error('Raw response text:', textResponse);

                  // Try to extract JSON from text response
                  try {
                    const jsonMatch = textResponse.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                      responseData = JSON.parse(jsonMatch[0]);
                      console.log('Extracted JSON from text response:', responseData);
                    } else {
                      // Create a basic response object if no JSON found
                      responseData = {
                        success: false,
                        error: `Invalid response format for ${file.name}`
                      };
                    }
                  } catch (extractError) {
                    console.error('Error extracting JSON from text:', extractError);
                    responseData = {
                      success: false,
                      error: `Failed to process response for ${file.name}`
                    };
                  }
                } catch (textError) {
                  console.error('Failed to get response text:', textError);
                  responseData = {
                    success: false,
                    error: `Failed to read response for ${file.name}`
                  };
                }
              }

              if (!response.ok) {
                console.error('API error response for', file.name, ':', responseData);

                // Special handling for Firebase Storage errors
                if (responseData.error && responseData.error.includes('Firebase Storage')) {
                  console.error('Firebase Storage error detected:', responseData.error);

                  // Throw specific error for Firebase Storage issues
                  throw new Error(`Firebase Storage error: ${responseData.error}`);
                }

                throw new Error(responseData.error || `Failed to analyze resume (Status: ${response.status})`);
              }

              console.log('API success response for', file.name, ':', responseData);

              // Check if the response indicates success
              if (responseData.success) {
                // Get the resumeUrl from the response
                let resumeUrl = responseData.resumeUrl;

                // Create fallback if data is missing
                if (!resumeUrl) {
                  console.warn('Missing resumeUrl in response, using fallback:', responseData);
                  // Generate a fallback URL that points to a placeholder
                  resumeUrl = `https://storage.googleapis.com/ats-match.firebasestorage.app/fallback/resume_${Date.now()}.pdf`;
                }

                // Get the resumeId from the response
                const resumeId = responseData.resumeId || `resume_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

                // Add to results with validated data
                newResults.push({
                  name: file.name,
                  url: resumeUrl
                });

                // Store the resumeId for later extraction status checking
                setUploadedResumeIds(prev => [...prev, resumeId]);

                // Track successful resume upload
                const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'unknown';
                trackResumeUpload(fileExtension, file.size, true);

                console.log(`Successfully uploaded ${file.name} with ID ${resumeId}`);
              } else {
                // Handle error response
                console.error(`Error response for ${file.name}:`, responseData);
                throw new Error(responseData.error || 'Unknown error occurred during upload');
              }
              apiSuccess = true;

            } catch (fetchError: any) {
              apiRetries++;
              lastError = fetchError instanceof Error
                ? fetchError
                : new Error(String(fetchError));

              console.error(`API attempt ${apiRetries} failed for ${file.name}:`, fetchError);

              // If we've reached max retries, throw the error
              if (apiRetries >= MAX_API_RETRIES) {
                console.error(`Maximum API retries (${MAX_API_RETRIES}) reached for ${file.name}`);
                throw lastError;
              }

              // Wait before retrying (exponential backoff)
              const delayMs = 1000 * Math.pow(2, apiRetries);
              console.log(`Waiting ${delayMs}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, delayMs));
            }
          }


        } catch (err) {
          console.error(`Error processing ${file.name}:`, err);

          // Track failed resume upload
          const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'unknown';
          trackResumeUpload(fileExtension, file.size, false);
          trackError('upload_failed', err instanceof Error ? err.message : 'Unknown error', 'resume-upload');

          // Provide more specific error messages based on the error type
          let errorMessage = err instanceof Error ? err.message : 'Unknown error';

          // Handle Firebase Storage specific errors
          if (errorMessage.includes('Firebase Storage')) {
            // Extract the specific Firebase error message if possible
            const firebaseErrorMatch = errorMessage.match(/Firebase Storage:(.+)/);
            const specificError = firebaseErrorMatch ? firebaseErrorMatch[1].trim() : 'unknown storage error';

            // Provide a more user-friendly message based on the specific error
            if (specificError.includes('unauthorized')) {
              errorMessage = `Permission denied while uploading ${file.name}. Please try logging out and back in.`;
            } else if (specificError.includes('network')) {
              errorMessage = `Network error while uploading ${file.name}. Please check your internet connection and try again.`;
            } else if (specificError.includes('format')) {
              errorMessage = `The format of ${file.name} is not supported. Please try a different file format (PDF, DOC, DOCX, or TXT).`;
            } else if (specificError.includes('quota')) {
              errorMessage = `Storage quota exceeded. Please contact support.`;
            } else {
              errorMessage = `Storage error while uploading ${file.name}. Please try again with a different file or format.`;
            }

            // Log detailed information for debugging
            console.error('Firebase Storage error details:', {
              fileName: file.name,
              fileSize: file.size,
              fileType: file.type,
              originalError: err instanceof Error ? err.message : 'Unknown error',
              specificError: specificError
            });

            // Try to suggest a solution based on the file
            const fileExt = file.name.split('.').pop()?.toLowerCase();
            if (fileExt === 'pdf') {
              console.log('Suggesting alternative for PDF file');
              errorMessage += ' Try converting the PDF to a simpler format or reducing its size.';
            } else if (['doc', 'docx'].includes(fileExt || '')) {
              console.log('Suggesting alternative for Word file');
              errorMessage += ' Try saving the document as a PDF and uploading again.';
            }
          }

          toast({
            title: "Upload error",
            description: `Failed to upload ${file.name}: ${errorMessage}`,
            variant: "destructive",
          });
        }
      }

      if (newResults.length === 0) {
        console.error("No files were successfully uploaded");

        toast({
          title: "Upload failed",
          description: "We couldn't upload any of the files. Please check the console for detailed error messages and try again with different files.",
          variant: "destructive",
        });

        // Clear the progress interval and hide dialog
        clearInterval(progressInterval);
        setShowUploadDialog(false);
        setIsUploading(false);

        // Return early instead of throwing an error
        return;
      }

      console.log(`Successfully uploaded ${newResults.length} file(s)`);

      // Show success message
      toast({
        title: "Upload complete",
        description: `${newResults.length} file(s) uploaded successfully`,
      });

      // Upload is complete, show the Analyse button in popup
      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadMessage("Upload complete! Click Analyze to process your resumes.");
      setUploadComplete(true);
      setIsUploading(false);
      setShowUploadDialog(true); // Now show the dialog with the Analyse button

      // Clear files
      setFiles([]);

    } catch (error) {
      console.error("Error uploading files:", error);

      // Provide more detailed error message
      let errorMessage = "Failed to upload files";
      if (error instanceof Error) {
        errorMessage = error.message;

        // Add more context for specific errors
        if (errorMessage.includes("Missing required fields")) {
          errorMessage = "Missing job information. Please create a job description first.";
        } else if (errorMessage.includes("Resume file is missing")) {
          errorMessage = "Resume file couldn't be uploaded. Please try a different file.";
        } else if (errorMessage.includes("Firebase Storage")) {
          errorMessage = "There was an issue storing your file. Please try a different file format or check your internet connection.";
        }
      }

      toast({
        title: "Upload failed",
        description: errorMessage,
        variant: "destructive",
      });

      // Clear the progress interval on error
      clearInterval(progressInterval);
      // Don't show dialog on error
      setShowUploadDialog(false);
    } finally {
      setIsUploading(false);
    }
  }, [files, jobId, jobData, toast, uploadProgress, uploadedResumeIds])

  // Handle Analyze button click
  const handleMatch = useCallback(async () => {
    // Set analyzing state to true
    setIsAnalyzing(true)
    setUploadComplete(false)
    setUploadProgress(0)
    setUploadMessage("Initiating resume extraction...")

    // Track analysis start
    const analysisStartTime = Date.now();

    try {
      // Get the job data to pass to the analyze endpoint
      const jobRef = doc(db, "jobs", jobId)
      const jobSnap = await getDoc(jobRef)

      if (!jobSnap.exists()) {
        throw new Error("Job not found")
      }

      const data = jobSnap.data()

      // Check if there are resumes to analyze
      if (!data.resumes || !Array.isArray(data.resumes) || data.resumes.length === 0) {
        throw new Error("No resumes found to analyze")
      }

      // Track analysis start with resume count
      trackAnalysisStart(data.resumes.length, 'individual');

      const currentUser = auth.currentUser

      if (!currentUser) {
        throw new Error("User is not authenticated")
      }

      // Get the current user's ID token
      const idToken = await currentUser.getIdToken()

      // Start the extraction process immediately
      setUploadProgress(1) // Start at 1%
      setUploadMessage("Starting resume data extraction...")

      // Define the extraction process function with batch processing
      const startExtractionProcess = async (jobData: any, user: { uid: string }, token: string) => {
        setUploadMessage("Preparing resume files for extraction...")

        const totalResumes = jobData.resumes.length
        let completedCount = 0

        // Filter out already extracted resumes
        const resumesToExtract = jobData.resumes.filter((resume: any) => resume.extractionStatus !== 'completed')
        const alreadyExtracted = totalResumes - resumesToExtract.length

        // Update progress for already extracted resumes
        if (alreadyExtracted > 0) {
          console.log(`${alreadyExtracted} resumes already extracted, skipping`)
          completedCount = alreadyExtracted
          const currentProgress = Math.min(1 + Math.floor((completedCount / totalResumes) * 69), 70)
          setUploadProgress(currentProgress)
        }

        if (resumesToExtract.length === 0) {
          console.log('All resumes already extracted, proceeding to final polling')
          setUploadProgress(95)
          setUploadMessage("All resumes processed. Finalizing...")
          await finalPollingPhase(totalResumes)
          return
        }

        // Get job details for analysis
        const jobDetails = {
          title: jobData.title || '',
          location: jobData.location || '',
          description: jobData.description || '',
          skills: Array.isArray(jobData.skills) ? jobData.skills.join(', ') : (typeof jobData.skills === 'string' ? jobData.skills : ''),
          minExperience: jobData.minExperience || undefined,
          maxExperience: jobData.maxExperience || undefined
        }

        console.log(`🚀 PRIMARY METHOD: Processing all ${resumesToExtract.length} resumes individually and concurrently`)

        try {
          // Use individual concurrent processing as primary method
          console.log(`🚀 Starting individual concurrent processing for ${resumesToExtract.length} resumes - PRIMARY METHOD`)
          setUploadMessage("🚀 Starting individual resume processing...")

          await processAllResumesConcurrently(resumesToExtract, jobId, user, token, jobDetails)

          console.log(`🎉 Individual concurrent processing completed for all ${resumesToExtract.length} resumes`)

          // Brief pause to show completion
          await new Promise(resolve => setTimeout(resolve, 1000))

          // Navigate directly to dashboard since processing is complete
          setUploadProgress(100)
          setUploadMessage("🎉 Processing complete! Redirecting to dashboard...")

          // Track analysis completion
          const analysisEndTime = Date.now();
          const processingTime = analysisEndTime - analysisStartTime;
          trackAnalysisComplete(resumesToExtract.length, processingTime, 'individual-primary');

          // Close the dialog immediately before redirection
          setShowUploadDialog(false)
          setIsAnalyzing(false)

          // Show success message
          toast({
            title: "Analysis complete",
            description: "Redirecting to dashboard where you can see the results",
          })

          // Navigate to the resume dashboard
          router.push(`/resume-dashboard?jobId=${jobId}`)

        } catch (error) {
          console.error(`❌ Individual processing (primary method) failed:`, error);

          // Show error message and complete with partial results
          setUploadProgress(95);
          setUploadMessage("⚠️ Processing failed, but some resumes may have been completed");

          // Show error toast
          toast({
            title: "Processing completed with errors",
            description: "Some resumes may not have been processed. Please check the dashboard and retry if needed.",
            variant: "destructive",
          });

          // Start final polling to ensure all analyses are complete
          await finalPollingPhase(totalResumes)
        }
      }







      // Helper function for final polling phase
      const finalPollingPhase = async (totalResumes: number) => {
        setUploadMessage("Finalizing analysis...")

        // Poll for the actual analysis status until all resumes are analyzed
        let allAnalysesComplete = false
        let pollCount = 0
        const MAX_POLLS = 150 // 150 * 2 seconds = 5 minutes max wait
        const POLL_INTERVAL = 2000 // Poll every 2 seconds

        // Motivational messages to show during analysis
        const analysisMessages = [
          "Almost done...",
          "Finalizing your results...",
          "Just a few more seconds...",
          "Getting everything ready...",
          "Putting the finishing touches...",
          "Nearly there...",
          "Wrapping things up...",
          "Almost finished..."
        ]

        while (!allAnalysesComplete && pollCount < MAX_POLLS) {
          // Wait 2 seconds between polls
          await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL))

          try {
            // Get the latest job data to check analysis status
            const jobRef = doc(db, "jobs", jobId)
            const jobSnap = await getDoc(jobRef)

            if (!jobSnap.exists()) {
              throw new Error("Job not found during analysis polling")
            }

            const latestJobData = jobSnap.data()

            if (!latestJobData.resumes || !Array.isArray(latestJobData.resumes)) {
              throw new Error("No resumes found during analysis polling")
            }

            // Check if all resumes have completed analysis
            const pendingAnalyses = latestJobData.resumes.filter(resume =>
              resume.extractionStatus === 'completed' &&
              (resume.analysisStatus !== 'completed' || resume.analyzed !== true)
            )

            console.log('Final polling analysis status:', {
              totalResumes: latestJobData.resumes.length,
              pendingAnalyses: pendingAnalyses.length,
              pollCount: pollCount + 1,
              maxPolls: MAX_POLLS
            })

            if (pendingAnalyses.length === 0) {
              // All analyses are complete
              allAnalysesComplete = true
              console.log("All resume analyses are complete!")
            } else {
              console.log(`Still waiting for ${pendingAnalyses.length} resume analyses to complete...`)

              // Show rotating motivational messages
              const completedCount = totalResumes - pendingAnalyses.length
              const messageIndex = Math.floor(pollCount / 3) % analysisMessages.length
              const baseMessage = analysisMessages[messageIndex]

              if (totalResumes > 1) {
                setUploadMessage(`${baseMessage} (${completedCount}/${totalResumes} complete)`)
              } else {
                setUploadMessage(baseMessage)
              }
            }

            pollCount++
          } catch (error) {
            console.error("Error polling for analysis status:", error)
            pollCount++

            // Show positive message even during errors
            const messageIndex = Math.floor(pollCount / 3) % analysisMessages.length
            setUploadMessage(analysisMessages[messageIndex])
          }
        }

        // Complete the process
        if (allAnalysesComplete) {
          setUploadProgress(100)
          setUploadMessage("Analysis complete! Redirecting to dashboard...")

          // Track analysis completion
          const analysisEndTime = Date.now();
          const processingTime = analysisEndTime - analysisStartTime;
          trackAnalysisComplete(totalResumes, processingTime, 'individual');

          // Close the dialog immediately before redirection
          setShowUploadDialog(false)
          setIsAnalyzing(false)

          // Show success message
          toast({
            title: "Analysis complete",
            description: "Redirecting to dashboard where you can see the results",
          })

          // Navigate to the resume dashboard
          router.push(`/resume-dashboard?jobId=${jobId}`)
        } else {
          // If we've reached the maximum polling attempts but analyses aren't complete
          console.error(`Reached maximum polling attempts (${MAX_POLLS}) but analysis not complete`)

          setUploadProgress(95)
          setUploadMessage("Analysis is taking longer than expected. Please wait or refresh the page to check status...")

          toast({
            title: "Analysis taking longer than expected",
            description: "The analysis is still running. Please wait a bit longer or refresh the page to check the status.",
            variant: "destructive",
          })
        }
      }

      // Helper function to process all resumes concurrently individually
      const processAllResumesConcurrently = async (resumes: any[], jobId: string, user: { uid: string }, token: string, jobDetails: any) => {
        console.log(`🚀 Starting individual concurrent processing for ${resumes.length} resumes`)

        let completedCount = 0
        let failedResumes: any[] = []

        // Create a shared counter for thread-safe progress tracking
        const progressTracker = {
          completed: 0,
          failed: 0,
          updateProgress: (success: boolean = true) => {
            if (success) {
              progressTracker.completed++
            } else {
              progressTracker.failed++
            }
            const totalProcessed = progressTracker.completed + progressTracker.failed
            const progressPercent = Math.floor((totalProcessed / resumes.length) * 100)
            setUploadProgress(progressPercent)

            if (progressTracker.failed > 0) {
              setUploadMessage(`📊 ${progressTracker.completed}/${resumes.length} completed, ${progressTracker.failed} failed`)
            } else {
              setUploadMessage(`✅ ${progressTracker.completed}/${resumes.length} resumes analyzed`)
            }
          }
        }

        // Create promises for all resumes at once
        const allPromises = resumes.map(async (resume: any, index: number) => {
          try {
            console.log(`🚀 Processing resume ${index + 1}/${resumes.length}: ${resume.name}`)

            const response = await fetch('/api/process-resume-complete', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                resumeUrl: resume.url,
                resumeName: resume.name,
                resumeId: resume.id,
                jobId: jobId,
                jobDetails: jobDetails,
                userId: user.uid
              })
            })

            if (response.ok) {
              const result = await response.json()
              console.log(`✅ Successfully processed ${resume.name}`)

              // Update progress for successful completion
              progressTracker.updateProgress(true)

              return { success: true, resumeId: resume.id, resumeName: resume.name, result }
            } else {
              const errorText = await response.text()
              console.error(`❌ Failed to process ${resume.name}:`, response.statusText, errorText)

              // Update progress for failed completion
              progressTracker.updateProgress(false)

              return { success: false, resumeId: resume.id, resumeName: resume.name, error: `${response.statusText}: ${errorText}` }
            }
          } catch (error) {
            console.error(`💥 Error processing ${resume.name}:`, error)

            // Update progress for failed completion
            progressTracker.updateProgress(false)

            return { success: false, resumeId: resume.id, resumeName: resume.name, error: error instanceof Error ? error.message : 'Unknown error' }
          }
        })

        // Wait for all promises to complete
        console.log(`⏳ Waiting for all ${resumes.length} API calls to complete...`)
        const results = await Promise.all(allPromises)

        // Separate successful and failed results
        const successfulResults = results.filter(result => result.success)
        failedResumes = results.filter(result => !result.success)

        console.log(`📊 Processing completed: ${successfulResults.length}/${resumes.length} successful, ${failedResumes.length} failed`)

        // Set final progress to 100%
        setUploadProgress(100)

        // Show final status message
        if (failedResumes.length > 0) {
          setUploadMessage(`⚠️ Completed with ${failedResumes.length} errors. ${successfulResults.length} resumes processed successfully.`)

          // Log failed resumes for debugging
          console.error('Failed resumes:', failedResumes.map(r => ({ name: r.resumeName, error: r.error })))
        } else {
          setUploadMessage(`🎉 All ${successfulResults.length} resumes processed successfully!`)
        }

        if (failedResumes.length > 0) {
          setUploadMessage(`🎉 Processing complete! ${successfulResults.length}/${resumes.length} resumes processed successfully`)
          console.warn(`⚠️ ${failedResumes.length} resumes could not be processed`)
        } else {
          setUploadMessage(`🎉 All ${resumes.length} resumes processed successfully!`)
        }
      }







      // Start the extraction process immediately after defining all functions
      await startExtractionProcess(data, currentUser, idToken)
    } catch (error) {
      console.error("Error analyzing resumes:", error)

      // Show error message
      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "Failed to analyze resumes",
        variant: "destructive",
      })

      // Close the dialog
      setShowUploadDialog(false)
      setIsAnalyzing(false)
    }
  }, [jobId, router, toast])

  if (!isMounted) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-black">
        <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-[#1aa8e0] animate-spin" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="mx-auto max-w-3xl p-6">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-[80vh]">
            <Loader2 className="h-8 w-8 animate-spin text-[#1aa8e0]" />
            <p className="mt-4 text-zinc-400">Loading...</p>
          </div>
        ) : (
          <>
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-lg font-normal text-[#1aa8e0]">
                Upload Resumes
              </h1>
            </div>

            <div className="relative">
              <div className="rounded-xl border border-zinc-800 bg-zinc-900/50 p-6 relative overflow-hidden">
                {/* Usage Display */}
                <div className="mb-4 p-3 bg-zinc-800 rounded-lg">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-zinc-300">
                      {formatFileSize(totalFileSize)} / 5 MB
                    </span>
                    <span className="text-zinc-300">
                      {files.length} / 50 resumes
                    </span>
                  </div>
                </div>

                {/* Upload Area */}
                <div
                  className={`border border-gray-700 rounded-lg p-6 text-center mb-6 transition-colors bg-[#1a1a1a] min-h-[180px] ${
                    isDragging ? "border-[#1aa8e0] bg-[#1aa8e0]/10" : "border-gray-700"
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="bg-[#2a2a2a] rounded-full p-3 w-14 h-14 mx-auto mb-3 flex items-center justify-center">
                    <Upload className="h-5 w-5 text-gray-400" />
                  </div>
                  <p className="text-gray-300 mb-1 text-base">Drag a file here, or</p>
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="text-[#1aa8e0] hover:underline text-sm">Choose a file to upload</span>
                    <input
                      id="file-upload"
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,.txt"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                  </label>
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-gray-500 text-xs">Supports DOC, DOCX, PDF and TXT up to 5MB</p>
                  </div>
                </div>

                {/* File List */}
                <div className="bg-gray-900 rounded-lg border border-gray-700 mb-6">
                  <div className="min-h-[320px] h-[320px] overflow-y-auto p-2">
                    {files.length > 0 ? (
                      <ul className="space-y-2">
                        {files.map((file, index) => (
                          <li
                            key={`${file.name}-${index}`}
                            className="flex items-center justify-between p-3 bg-gray-800 rounded"
                          >
                            <div className="flex items-center">
                              <FileText className="h-5 w-5 mr-3 text-gray-400" />
                              <span className="font-normal text-sm">{file.name}</span>
                            </div>
                            <div className="flex items-center space-x-4">
                              <label className="cursor-pointer text-[#1aa8e0] hover:underline text-xs">
                                Change
                                <input
                                  type="file"
                                  accept=".pdf,.doc,.docx,.txt"
                                  className="hidden"
                                  onChange={(e) => handleReplaceFile(index, e)}
                                />
                              </label>
                              <button
                                onClick={() => handleRemoveFile(index)}
                                className="text-red-400 hover:underline text-xs"
                              >
                                Remove
                              </button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div className="text-center flex items-center justify-center h-full text-gray-500">No files uploaded yet</div>
                    )}
                  </div>
                </div>

                {/* Buttons */}
                <div className="flex justify-end space-x-4 mt-5">
                  <Button
                    type="button"
                    variant="outline"
                    className="h-10 px-6 border-zinc-700 bg-zinc-800 text-white hover:bg-zinc-700"
                    onClick={() => {
                      setFiles([])
                      setTotalFileSize(0)
                    }}
                    disabled={isUploading || files.length === 0}
                  >
                    Clear
                  </Button>
                  <Button
                    onClick={handleUpload}
                    className="h-10 px-6 hover:bg-opacity-90 text-white"
                    style={{ backgroundColor: "#1aa8e0" }}
                    disabled={isUploading || files.length === 0}
                  >
                    {isUploading ? (
                      <>
                        <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-white animate-spin mr-2" />
                        Uploading...
                      </>
                    ) : (
                      "Upload Resumes"
                    )}
                  </Button>
                </div>
              </div>
            </div>

            {/* Upload Progress Dialog */}
            <Dialog
              open={showUploadDialog}
              onOpenChange={(open) => {
                // Only allow closing the dialog if upload is not complete
                // This ensures users see and use the Analyze button
                if (uploadComplete) {
                  // Don't close the dialog when clicking outside if upload is complete
                  return;
                }
                setShowUploadDialog(open);
              }}
            >
              <DialogContent
                className="bg-gray-900 border-gray-800 text-white max-w-sm mx-auto"
                onEscapeKeyDown={(e) => {
                  // Prevent closing with Escape key if upload is complete
                  if (uploadComplete) {
                    e.preventDefault();
                  }
                }}
                onPointerDownOutside={(e) => {
                  // Prevent closing when clicking outside if upload is complete
                  if (uploadComplete) {
                    e.preventDefault();
                  }
                }}
              >
                <DialogTitle className="sr-only">
                  {uploadComplete ? "Upload Complete" : "Upload Progress"}
                </DialogTitle>
                <div className="flex flex-col items-center justify-center py-6">
                  <CircularProgress
                    value={uploadProgress}
                    size={150}
                    strokeWidth={10}
                    className="mb-4"
                  >
                    {uploadComplete && !isAnalyzing ? (
                      <Button
                        onClick={handleMatch}
                        className="bg-[#8529db] text-white hover:bg-[#7020c9] rounded-full p-0 flex items-center justify-center shadow-lg cursor-pointer transition-all duration-200 transform hover:scale-105 active:scale-95"
                        style={{
                          width: "80%",
                          height: "40%",
                          margin: "0 auto",
                          fontSize: "1.1rem",
                          fontWeight: "bold",
                          backgroundColor: "#8529db", // Brand color purple
                          boxShadow: "0 0 20px rgba(133, 41, 219, 0.5)"
                        }}
                      >
                        Analyze
                      </Button>
                    ) : (
                      <div className="flex flex-col items-center justify-center">
                        <span className="text-white text-sm">{uploadProgress}%</span>
                        {isAnalyzing && (
                          <span className="text-[#1aa8e0] text-xs mt-1">
                            Processing...
                          </span>
                        )}
                      </div>
                    )}
                  </CircularProgress>

                  <p className="text-gray-300 mt-4 text-center">
                    {uploadMessage}
                  </p>
                </div>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
    </div>
  )
}
