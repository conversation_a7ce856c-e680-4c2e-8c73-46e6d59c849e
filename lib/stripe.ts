import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-11-20.acacia',
  typescript: true,
});

// Stripe configuration constants
export const STRIPE_CONFIG = {
  PRO_PLAN_PRICE_ID: 'price_pro_monthly', // We'll create this in Stripe Dashboard
  PLUS_PLAN_PRICE_ID: 'price_plus_monthly', // We'll create this in Stripe Dashboard
  CURRENCY: 'inr',
  PRO_PLAN_AMOUNT: 69900, // ₹699 in paise (smallest currency unit for INR)
  PLUS_PLAN_AMOUNT: 149900, // ₹1499 in paise (smallest currency unit for INR)
} as const;

// Product configuration
export const PRODUCTS = {
  PRO: {
    name: 'Pro Plan',
    description: 'Ideal for individual HR professionals',
    features: [
      '50 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Delete Resume after 180 days',
      'Priority support',
      '1 user only',
    ],
    price: 699,
    currency: 'inr',
    interval: 'month',
  },
  PLUS: {
    name: 'Plus Plan',
    description: 'Perfect for growing HR teams',
    features: [
      '100 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Delete Resume after 180 days',
      'Priority support',
      '1 user only',
    ],
    price: 1499,
    currency: 'inr',
    interval: 'month',
  },
} as const;
